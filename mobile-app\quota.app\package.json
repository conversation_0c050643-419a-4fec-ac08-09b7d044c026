{"name": "quota.app", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start --port 3003", "android": "expo run:android --port 3003", "ios": "expo start --ios --port 3003", "web": "expo start --web --port 3003", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "expo": "~53.0.9", "expo-av": "^15.1.4", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.4", "expo-camera": "^16.1.6", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-media-library": "^17.1.6", "expo-notifications": "^0.31.2", "expo-permissions": "^14.4.0", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-animatable": "^1.4.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.24.0", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-permissions": "^5.4.0", "react-native-qrcode-scanner": "^1.5.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}