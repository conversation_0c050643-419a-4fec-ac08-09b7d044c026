spring.application.name=backend

# Primary Database Configuration (quota.app)
spring.datasource.url=**************************************************************************************
spring.datasource.username=admindatabasequotaapp
spring.datasource.password=zNAj8TWuTh6:mfA
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.jdbc-url=${spring.datasource.url}

# DMT Database Configuration
spring.datasource.dmt.url=*******************************************************************
spring.datasource.dmt.username=admindatabasedmt
spring.datasource.dmt.password=zNAj8TWuTh6:mfA
spring.datasource.dmt.driver-class-name=org.postgresql.Driver
spring.datasource.dmt.jdbc-url=${spring.datasource.dmt.url}

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
# spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.physical_naming_strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.implicit_naming_strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl

# Server Configuration
server.port=8888

# JWT Configuration
jwt.secret=RyiCy94L77d'XlqcJ;-7Zd;$pdGC01PQ,!A,f;E9-}YkD(V^3U
jwt.expiration=86400000

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=ujug tpve gqur jezw
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.smtp.connectiontimeout=10000
spring.mail.properties.mail.smtp.timeout=10000
spring.mail.properties.mail.smtp.writetimeout=10000
spring.mail.properties.mail.debug=true
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback=false

# Session Configuration
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.session.timeout=30m
spring.session.jdbc.table-name=SPRING_SESSION
spring.session.jdbc.schema=classpath:org/springframework/session/jdbc/schema-postgresql.sql
# Ensure Spring Session uses the primary DataSource
spring.session.jdbc.platform=postgresql
# Set session cookie name
server.servlet.session.cookie.name=QUOTA_APP_SESSION
# Set session cookie secure flag (enable in production)
# server.servlet.session.cookie.secure=true
# Set session cookie HTTP-only flag
server.servlet.session.cookie.http-only=true
# Set session cookie same-site attribute
server.servlet.session.cookie.same-site=lax

# Twilio Configuration for SMS notifications (Mobile App)
twilio.account.sid=**********************************
twilio.auth.token=747077f5a0ae7cf5deed2445a43b7dff
twilio.phone.number=+***********
twilio.enabled=true

# Logging Configuration
spring.main.banner-mode=off
logging.level.root=WARN
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.web.cors=TRACE
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.session=DEBUG
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql=WARN
logging.level.com.quotaapp.backend=DEBUG
logging.level.com.quotaapp.backend.filter=TRACE