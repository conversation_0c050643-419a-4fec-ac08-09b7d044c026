/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/HistoryNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/NotificationsNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/SettingsNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/index`; params?: Router.UnknownInputParams; } | { pathname: `/../src/hooks/useCamera`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/navigation/HistoryNavigator`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/navigation/NotificationsNavigator`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/navigation/SettingsNavigator`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/navigation/index`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/hooks/useCamera`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/../src/components/navigation/HistoryNavigator${`?${string}` | `#${string}` | ''}` | `/../src/components/navigation/NotificationsNavigator${`?${string}` | `#${string}` | ''}` | `/../src/components/navigation/SettingsNavigator${`?${string}` | `#${string}` | ''}` | `/../src/components/navigation/index${`?${string}` | `#${string}` | ''}` | `/../src/hooks/useCamera${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/HistoryNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/NotificationsNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/SettingsNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/navigation/index`; params?: Router.UnknownInputParams; } | { pathname: `/../src/hooks/useCamera`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; };
    }
  }
}
